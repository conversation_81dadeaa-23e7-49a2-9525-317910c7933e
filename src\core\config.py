"""
Simple Data Service Configuration Management.
Refactored from app.core.config for simplified database service.
"""

import os
from typing import Optional, List
from pydantic import Field
from pydantic_settings import BaseSettings
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()


class DatabaseSettings(BaseSettings):
    """Database configuration settings."""

    host: str = Field(default="localhost", env="DATABASE_HOST")
    port: int = Field(default=5432, env="DATABASE_PORT")
    name: str = Field(default="simple_dataservice_db", env="DATABASE_NAME")
    user: str = Field(default="postgres", env="DATABASE_USER")
    password: str = Field(default="admin", env="DATABASE_PASSWORD")
    timescale_enabled: bool = True

    model_config = {
        "env_file": ".env",
        "case_sensitive": False,
        "extra": "ignore"
    }

    @property
    def url(self) -> str:
        """Get database URL."""
        return f"postgresql://{self.user}:{self.password}@{self.host}:{self.port}/{self.name}"


class FyersSettings(BaseSettings):
    """Fyers API configuration settings."""

    client_id: str = Field(default="", env="FYERS_CLIENT_ID")
    secret_key: str = Field(default="", env="FYERS_SECRET_KEY")
    redirect_uri: str = Field(default="http://localhost:8080/callback", env="FYERS_REDIRECT_URI")
    access_token: Optional[str] = Field(default=None, env="FYERS_ACCESS_TOKEN")

    model_config = {
        "env_file": ".env",
        "case_sensitive": False,
        "extra": "ignore"
    }


class APISettings(BaseSettings):
    """API configuration settings."""

    host: str = Field(default="0.0.0.0", env="API_HOST")
    port: int = Field(default=8000, env="API_PORT")
    debug: bool = Field(default=False, env="DEBUG")
    secret_key: str = Field(default="default_secret_key", env="SECRET_KEY")

    model_config = {
        "env_file": ".env",
        "case_sensitive": False,
        "extra": "ignore"
    }


class Settings(BaseSettings):
    """Main application settings."""
    
    # Sub-configurations
    database: DatabaseSettings = DatabaseSettings()
    fyers: FyersSettings = FyersSettings()
    api: APISettings = APISettings()
    
    # Application settings
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    
    model_config = {
        "env_file": ".env",
        "case_sensitive": False,
        "extra": "ignore"
    }


# Global settings instance
settings = Settings()


def get_settings() -> Settings:
    """Get application settings."""
    return settings
