import asyncio
import pandas as pd
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import text, and_
from sqlalchemy.dialects.postgresql import insert
import logging

from models.market_data import EquityOHLCV, IndexOHLCV, MarketType
from database.connection import get_db_session
from core.exceptions import DataStorageError, ValidationError

logger = logging.getLogger(__name__)

class DataStorageService:
    """High-performance data storage service for market data."""
    
    def __init__(self):
        self.batch_size = 10000
        self.timezone = 'Asia/Kolkata'
    
    async def store_ohlcv_bulk(
        self, 
        market_type: MarketType, 
        data: List[Dict], 
        upsert: bool = True
    ) -> Dict[str, int]:
        """Store OHLCV data in bulk with upsert capability."""
        try:
            if not data:
                return {"inserted": 0, "updated": 0}
            
            # Get appropriate model based on market type
            model_class = self._get_model_class(market_type)
            table_name = model_class.__tablename__
            
            # Process data in batches
            total_inserted = 0
            total_updated = 0
            
            for i in range(0, len(data), self.batch_size):
                batch = data[i:i + self.batch_size]
                inserted, updated = await self._store_batch(
                    table_name, batch, upsert
                )
                total_inserted += inserted
                total_updated += updated
                
                logger.info(f"Processed batch {i//self.batch_size + 1}: "
                           f"{inserted} inserted, {updated} updated")
            
            return {"inserted": total_inserted, "updated": total_updated}
            
        except Exception as e:
            logger.error(f"Error in bulk storage: {e}")
            raise DataStorageError(f"Failed to store data: {e}")
    
    async def _store_batch(
        self, 
        table_name: str, 
        batch: List[Dict], 
        upsert: bool
    ) -> Tuple[int, int]:
        """Store a single batch with optimized upsert."""
        async with get_db_session() as session:
            try:
                if upsert:
                    # Use PostgreSQL UPSERT for efficient conflict resolution
                    stmt = text(f"""
                        INSERT INTO {table_name} 
                        (datetime, symbol, open, high, low, close, volume)
                        VALUES (:datetime, :symbol, :open, :high, :low, :close, :volume)
                        ON CONFLICT (datetime, symbol) 
                        DO UPDATE SET
                            open = EXCLUDED.open,
                            high = EXCLUDED.high,
                            low = EXCLUDED.low,
                            close = EXCLUDED.close,
                            volume = EXCLUDED.volume,
                            created_at = NOW()
                        RETURNING (xmax = 0) AS inserted
                    """)
                else:
                    stmt = text(f"""
                        INSERT INTO {table_name} 
                        (datetime, symbol, open, high, low, close, volume)
                        VALUES (:datetime, :symbol, :open, :high, :low, :close, :volume)
                        ON CONFLICT (datetime, symbol) DO NOTHING
                        RETURNING 1 as inserted
                    """)
                
                result = await session.execute(stmt, batch)
                results = result.fetchall()
                
                if upsert:
                    inserted = sum(1 for r in results if r.inserted)
                    updated = len(results) - inserted
                else:
                    inserted = len(results)
                    updated = 0
                
                await session.commit()
                return inserted, updated
                
            except Exception as e:
                await session.rollback()
                raise e
    
    async def get_ohlcv_range(
        self,
        market_type: MarketType,
        symbol: str,
        start_date: datetime,
        end_date: datetime,
        timeframe: str = "1m"
    ) -> List[Dict]:
        """Get OHLCV data for date range with optional resampling."""
        try:
            model_class = self._get_model_class(market_type)
            
            async with get_db_session() as session:
                if timeframe == "1m":
                    # Direct query for 1-minute data
                    query = session.query(model_class).filter(
                        and_(
                            model_class.symbol == symbol,
                            model_class.datetime >= start_date,
                            model_class.datetime <= end_date
                        )
                    ).order_by(model_class.datetime)
                    
                    result = await session.execute(query)
                    return [self._row_to_dict(row) for row in result.scalars()]
                else:
                    # Resample to higher timeframe
                    return await self._resample_data(
                        session, model_class, symbol, start_date, end_date, timeframe
                    )
                    
        except Exception as e:
            logger.error(f"Error getting OHLCV range: {e}")
            raise DataStorageError(f"Failed to get data: {e}")
    
    def _get_model_class(self, market_type: MarketType):
        """Get SQLAlchemy model class based on market type."""
        model_map = {
            MarketType.EQUITY: EquityOHLCV,
            MarketType.INDEX: IndexOHLCV,
            # Add futures and options models when implemented
        }
        return model_map.get(market_type)