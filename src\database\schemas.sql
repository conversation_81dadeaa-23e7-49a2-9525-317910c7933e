-- Enable TimescaleDB extension
CREATE EXTENSION IF NOT EXISTS timescaledb;

-- Market segments tables with optimized schemas
-- 1. Cash Market - Equities
CREATE TABLE IF NOT EXISTS equity_ohlcv (
    datetime TIMESTAMPTZ NOT NULL,
    symbol VARCHAR(50) NOT NULL,
    open DECIMAL(12,4) NOT NULL,
    high DECIMAL(12,4) NOT NULL,
    low DECIMAL(12,4) NOT NULL,
    close DECIMAL(12,4) NOT NULL,
    volume BIGINT NOT NULL DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    PRIMARY KEY (datetime, symbol)
);

-- 2. Cash Market - Indices
CREATE TABLE IF NOT EXISTS index_ohlcv (
    datetime TIMESTAMPTZ NOT NULL,
    symbol VARCHAR(50) NOT NULL,
    open DECIMAL(12,4) NOT NULL,
    high DECIMAL(12,4) NOT NULL,
    low DECIMAL(12,4) NOT NULL,
    close DECIMAL(12,4) NOT NULL,
    volume BIGINT NOT NULL DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    PRIMARY KEY (datetime, symbol)
);

-- 3. Futures contracts
CREATE TABLE IF NOT EXISTS futures_ohlcv (
    datetime TIMESTAMPTZ NOT NULL,
    symbol VARCHAR(100) NOT NULL,
    expiry_date DATE NOT NULL,
    open DECIMAL(12,4) NOT NULL,
    high DECIMAL(12,4) NOT NULL,
    low DECIMAL(12,4) NOT NULL,
    close DECIMAL(12,4) NOT NULL,
    volume BIGINT NOT NULL DEFAULT 0,
    open_interest BIGINT DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    PRIMARY KEY (datetime, symbol, expiry_date)
);

-- 4. Options contracts
CREATE TABLE IF NOT EXISTS options_ohlcv (
    datetime TIMESTAMPTZ NOT NULL,
    symbol VARCHAR(100) NOT NULL,
    expiry_date DATE NOT NULL,
    strike_price DECIMAL(12,4) NOT NULL,
    option_type VARCHAR(2) NOT NULL CHECK (option_type IN ('CE', 'PE')),
    open DECIMAL(12,4) NOT NULL,
    high DECIMAL(12,4) NOT NULL,
    low DECIMAL(12,4) NOT NULL,
    close DECIMAL(12,4) NOT NULL,
    volume BIGINT NOT NULL DEFAULT 0,
    open_interest BIGINT DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    PRIMARY KEY (datetime, symbol, expiry_date, strike_price, option_type)
);

-- Convert to hypertables for time-series optimization
SELECT create_hypertable('equity_ohlcv', 'datetime', chunk_time_interval => INTERVAL '1 day');
SELECT create_hypertable('index_ohlcv', 'datetime', chunk_time_interval => INTERVAL '1 day');
SELECT create_hypertable('futures_ohlcv', 'datetime', chunk_time_interval => INTERVAL '1 day');
SELECT create_hypertable('options_ohlcv', 'datetime', chunk_time_interval => INTERVAL '1 day');

-- Optimized indexes for fast queries
CREATE INDEX IF NOT EXISTS idx_equity_symbol_datetime ON equity_ohlcv (symbol, datetime DESC);
CREATE INDEX IF NOT EXISTS idx_index_symbol_datetime ON index_ohlcv (symbol, datetime DESC);
CREATE INDEX IF NOT EXISTS idx_futures_symbol_datetime ON futures_ohlcv (symbol, datetime DESC);
CREATE INDEX IF NOT EXISTS idx_options_symbol_datetime ON options_ohlcv (symbol, datetime DESC);