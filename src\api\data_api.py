from fastapi import APIRouter, HTTPException, Query, Depends
from fastapi.responses import JSONResponse
from typing import List, Optional
from datetime import datetime, timedelta
import asyncio

from services.data_storage_service import DataStorageService
from services.fyers_integration_service import FyersIntegrationService
from models.market_data import MarketType, OHLCVBase, EquityOHLCVResponse
from core.dependencies import get_data_storage_service, get_fyers_service

router = APIRouter(prefix="/api/v1/data", tags=["Market Data"])

@router.post("/store/{market_type}")
async def store_market_data(
    market_type: MarketType,
    data: List[OHLCVBase],
    upsert: bool = Query(True, description="Enable upsert mode"),
    storage_service: DataStorageService = Depends(get_data_storage_service)
):
    """Store OHLCV data in bulk."""
    try:
        data_dicts = [item.dict() for item in data]
        result = await storage_service.store_ohlcv_bulk(
            market_type=market_type,
            data=data_dicts,
            upsert=upsert
        )
        return JSONResponse(
            status_code=201,
            content={
                "message": "Data stored successfully",
                "statistics": result
            }
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/query/{market_type}/{symbol}")
async def get_market_data(
    market_type: MarketType,
    symbol: str,
    start_date: datetime = Query(..., description="Start date (ISO format)"),
    end_date: datetime = Query(..., description="End date (ISO format)"),
    timeframe: str = Query("1m", description="Timeframe (1m, 5m, 15m, 1h, 1d)"),
    storage_service: DataStorageService = Depends(get_data_storage_service)
):
    """Get OHLCV data for symbol and date range."""
    try:
        data = await storage_service.get_ohlcv_range(
            market_type=market_type,
            symbol=symbol,
            start_date=start_date,
            end_date=end_date,
            timeframe=timeframe
        )
        return JSONResponse(content={"data": data, "count": len(data)})
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/load/{market_type}/{symbol}")
async def load_historical_data(
    market_type: MarketType,
    symbol: str,
    years: int = Query(1, description="Number of years to load"),
    resume: bool = Query(True, description="Resume from last available data"),
    fyers_service: FyersIntegrationService = Depends(get_fyers_service),
    storage_service: DataStorageService = Depends(get_data_storage_service)
):
    """Load historical data from Fyers API with resumption capability."""
    try:
        result = await fyers_service.load_historical_data_with_resume(
            market_type=market_type,
            symbol=symbol,
            years=years,
            resume=resume,
            storage_service=storage_service
        )
        return JSONResponse(content=result)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))