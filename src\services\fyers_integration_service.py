import asyncio
import aiohttp
import pandas as pd
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import logging
from pathlib import Path
import sys

# Import from reference codebase
sys.path.append("C:/Users/<USER>/Desktop/Python/signal_stack")
from fyers_auth import FyersAuth  # Import from reference codebase

from models.market_data import MarketType
from core.config import settings
from core.exceptions import FyersAPIError, DataIntegrityError

logger = logging.getLogger(__name__)

class FyersIntegrationService:
    """Enhanced Fyers integration with resumption and error handling."""
    
    def __init__(self):
        self.fyers_auth = FyersAuth()
        self.session = None
        self.max_retries = 3
        self.retry_delay = 5
        self.chunk_size_days = 90
    
    async def initialize(self) -> bool:
        """Initialize Fyers API connection."""
        try:
            success = await self.fyers_auth.authenticate()
            if success:
                self.session = aiohttp.ClientSession()
                logger.info("Fyers API initialized successfully")
                return True
            return False
        except Exception as e:
            logger.error(f"Failed to initialize Fyers API: {e}")
            return False
    
    async def load_historical_data_with_resume(
        self,
        market_type: MarketType,
        symbol: str,
        years: int,
        resume: bool,
        storage_service
    ) -> Dict:
        """Load historical data with automatic resumption."""
        try:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=years * 365)
            
            if resume:
                # Find last available data point
                last_data_point = await storage_service.get_last_data_point(
                    market_type, symbol
                )
                if last_data_point:
                    start_date = last_data_point + timedelta(minutes=1)
                    logger.info(f"Resuming from {start_date} for {symbol}")
            
            # Load data in chunks with error handling
            total_records = 0
            current_date = start_date
            failed_chunks = []
            
            while current_date < end_date:
                chunk_end = min(
                    current_date + timedelta(days=self.chunk_size_days),
                    end_date
                )
                
                try:
                    chunk_data = await self._fetch_chunk_with_retry(
                        symbol, current_date, chunk_end
                    )
                    
                    if chunk_data:
                        result = await storage_service.store_ohlcv_bulk(
                            market_type=market_type,
                            data=chunk_data,
                            upsert=True
                        )
                        total_records += result["inserted"] + result["updated"]
                        logger.info(f"Loaded chunk {current_date.date()} to {chunk_end.date()}: "
                                   f"{len(chunk_data)} records")
                    
                except Exception as e:
                    logger.error(f"Failed to load chunk {current_date.date()}: {e}")
                    failed_chunks.append((current_date, chunk_end))
                
                current_date = chunk_end + timedelta(days=1)
                await asyncio.sleep(1)  # Rate limiting
            
            # Retry failed chunks
            if failed_chunks:
                logger.info(f"Retrying {len(failed_chunks)} failed chunks")
                await self._retry_failed_chunks(
                    failed_chunks, symbol, market_type, storage_service
                )
            
            return {
                "symbol": symbol,
                "total_records": total_records,
                "failed_chunks": len(failed_chunks),
                "status": "completed"
            }
            
        except Exception as e:
            logger.error(f"Error in historical data loading: {e}")
            raise FyersAPIError(f"Failed to load historical data: {e}")
    
    async def _fetch_chunk_with_retry(
        self, 
        symbol: str, 
        start_date: datetime, 
        end_date: datetime
    ) -> List[Dict]:
        """Fetch data chunk with retry mechanism."""
        for attempt in range(self.max_retries):
            try:
                data = await self._fetch_fyers_data(symbol, start_date, end_date)
                return self._validate_and_clean_data(data)
            except Exception as e:
                if attempt < self.max_retries - 1:
                    logger.warning(f"Attempt {attempt + 1} failed for {symbol}: {e}")
                    await asyncio.sleep(self.retry_delay * (attempt + 1))
                else:
                    raise e
        return []
    
    async def _fetch_fyers_data(
        self, 
        symbol: str, 
        start_date: datetime, 
        end_date: datetime
    ) -> List[Dict]:
        """Fetch data from Fyers API."""
        # Implementation using authenticated Fyers API
        fyers_symbol = self._convert_to_fyers_symbol(symbol)
        
        data_params = {
            "symbol": fyers_symbol,
            "resolution": "1",
            "date_format": "1",
            "range_from": int(start_date.timestamp()),
            "range_to": int(end_date.timestamp()),
            "cont_flag": "1"
        }
        
        response = await self.fyers_auth.fyers.history(data_params)
        
        if response["s"] == "ok":
            return self._parse_fyers_response(response)
        else:
            raise FyersAPIError(f"Fyers API error: {response.get('message', 'Unknown error')}")