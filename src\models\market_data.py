from sqlalchemy import Column, String, DateTime, Numeric, BigInteger, Date, CheckConstraint
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.dialects.postgresql import TIMESTAMPTZ
from pydantic import BaseModel, Field
from datetime import datetime
from typing import Optional
from enum import Enum

Base = declarative_base()

class MarketType(str, Enum):
    EQUITY = "equity"
    INDEX = "index"
    FUTURES = "futures"
    OPTIONS = "options"

class OptionType(str, Enum):
    CALL = "CE"
    PUT = "PE"

# SQLAlchemy Models
class EquityOHLCV(Base):
    __tablename__ = "equity_ohlcv"
    
    datetime = Column(TIMESTAMPTZ, primary_key=True)
    symbol = Column(String(50), primary_key=True)
    open = Column(Numeric(12, 4), nullable=False)
    high = Column(Numeric(12, 4), nullable=False)
    low = Column(Numeric(12, 4), nullable=False)
    close = Column(Numeric(12, 4), nullable=False)
    volume = Column(BigInteger, default=0)
    created_at = Column(TIMESTAMPTZ, default=datetime.utcnow)

class IndexOHLCV(Base):
    __tablename__ = "index_ohlcv"
    
    datetime = Column(TIMESTAMPTZ, primary_key=True)
    symbol = Column(String(50), primary_key=True)
    open = Column(Numeric(12, 4), nullable=False)
    high = Column(Numeric(12, 4), nullable=False)
    low = Column(Numeric(12, 4), nullable=False)
    close = Column(Numeric(12, 4), nullable=False)
    volume = Column(BigInteger, default=0)
    created_at = Column(TIMESTAMPTZ, default=datetime.utcnow)

# Pydantic Models for API
class OHLCVBase(BaseModel):
    datetime: datetime
    symbol: str
    open: float = Field(..., gt=0)
    high: float = Field(..., gt=0)
    low: float = Field(..., gt=0)
    close: float = Field(..., gt=0)
    volume: int = Field(default=0, ge=0)

class EquityOHLCVCreate(OHLCVBase):
    pass

class EquityOHLCVResponse(OHLCVBase):
    created_at: datetime
    
    class Config:
        from_attributes = True